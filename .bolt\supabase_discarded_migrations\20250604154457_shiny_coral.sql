/*
  # Add Profile Creation Trigger

  1. New Functions
    - `handle_new_user`: Creates a profile entry when a new user signs up
  
  2. New Triggers
    - `on_auth_user_created`: Triggers profile creation on new user signup
*/

-- Create a function to handle new user signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
R<PERSON><PERSON><PERSON> trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  INSERT INTO public.profiles (id, email, full_name)
  VALUES (
    NEW.id,
    NEW.email,
    NEW.raw_user_meta_data->>'full_name'
  );
  RETURN NEW;
END;
$$;

-- Create a trigger to call this function after a user signs up
CREATE OR REPLACE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();